import { BatterySettings } from './BatterySettings';

/**
 * 電池設定 UI 管理類
 */
export class BatterySettingsUI {
    private settings: BatterySettings;
    private container: HTMLElement | null = null;

    constructor() {
        this.settings = BatterySettings.getInstance();
    }

    /**
     * 創建設定 UI
     */
    public createSettingsUI(container: HTMLElement): void {
        this.container = container;
        const currentSettings = this.settings.getSettings();
        
        container.innerHTML = `
            <div class="battery-settings-panel">
                <h3>電池檢測設定</h3>
                
                <!-- 基本檢測標準 -->
                <div class="settings-section">
                    <h4>基本檢測標準</h4>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="packVoltageLimit">Pack Voltage Limit (mV):</label>
                            <input type="number" id="packVoltageLimit" value="${currentSettings.packVoltageLimit}" 
                                   min="1000" max="10000" step="100">
                        </div>
                        <div class="setting-item">
                            <label for="cellVoltage">Cell Voltage (mV):</label>
                            <input type="number" id="cellVoltage" value="${currentSettings.cellVoltage}" 
                                   min="500" max="5000" step="50">
                        </div>
                        <div class="setting-item">
                            <label for="unbalanceVoltage">Unbalance Voltage (mV):</label>
                            <input type="number" id="unbalanceVoltage" value="${currentSettings.unbalanceVoltage}" 
                                   min="100" max="2000" step="50">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLimit">Cycle Count Limit:</label>
                            <input type="number" id="cycleCountLimit" value="${currentSettings.cycleCountLimit}" 
                                   min="100" max="2000" step="50">
                        </div>
                        <div class="setting-item">
                            <label for="temperatureLimit">Temperature Limit (°C):</label>
                            <input type="number" id="temperatureLimit" value="${currentSettings.temperatureLimit}" 
                                   min="0" max="100" step="5">
                        </div>
                    </div>
                </div>

                <!-- 循環計數等級 -->
                <div class="settings-section">
                    <h4>循環計數健康度等級 (%)</h4>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="cycleCountLevel1">Level 1:</label>
                            <input type="number" id="cycleCountLevel1" value="${currentSettings.cycleCountLevel1}" 
                                   min="0" max="100" step="0.01">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLevel2">Level 2:</label>
                            <input type="number" id="cycleCountLevel2" value="${currentSettings.cycleCountLevel2}" 
                                   min="0" max="100" step="0.01">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLevel3">Level 3:</label>
                            <input type="number" id="cycleCountLevel3" value="${currentSettings.cycleCountLevel3}" 
                                   min="0" max="100" step="0.01">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLevel4">Level 4:</label>
                            <input type="number" id="cycleCountLevel4" value="${currentSettings.cycleCountLevel4}" 
                                   min="0" max="100" step="0.01">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLevel5">Level 5:</label>
                            <input type="number" id="cycleCountLevel5" value="${currentSettings.cycleCountLevel5}" 
                                   min="0" max="100" step="0.01">
                        </div>
                        <div class="setting-item">
                            <label for="cycleCountLevel6">Level 6:</label>
                            <input type="number" id="cycleCountLevel6" value="${currentSettings.cycleCountLevel6}" 
                                   min="0" max="100" step="0.01">
                        </div>
                    </div>
                </div>

                <!-- 電池隔離標準 -->
                <div class="settings-section">
                    <h4>電池隔離標準</h4>
                    <div class="settings-grid">
                        <div class="setting-item">
                            <label for="isolateCycle">Isolate Cycle:</label>
                            <input type="number" id="isolateCycle" value="${currentSettings.isolateCycle}" 
                                   min="100" max="2000" step="50">
                        </div>
                        <div class="setting-item">
                            <label for="isolateHealth">Isolate Health (%):</label>
                            <input type="number" id="isolateHealth" value="${currentSettings.isolateHealth}" 
                                   min="0" max="100" step="1">
                        </div>
                        <div class="setting-item">
                            <label for="isolateYearTime">Isolate Year Time:</label>
                            <input type="number" id="isolateYearTime" value="${currentSettings.isolateYearTime}" 
                                   min="1" max="10" step="1">
                        </div>
                    </div>
                </div>

                <!-- 位置列表 -->
                <div class="settings-section">
                    <h4>位置列表</h4>
                    <div class="list-manager">
                        <div class="list-input">
                            <input type="text" id="newLocation" placeholder="新增位置...">
                            <button type="button" id="addLocation">新增</button>
                        </div>
                        <ul id="locationList" class="settings-list">
                            ${currentSettings.locationList.map(location => 
                                `<li>${location} <button type="button" class="remove-btn" data-location="${location}">移除</button></li>`
                            ).join('')}
                        </ul>
                    </div>
                </div>

                <!-- 損壞原因列表 -->
                <div class="settings-section">
                    <h4>損壞原因列表</h4>
                    <div class="list-manager">
                        <div class="list-input">
                            <input type="text" id="newBrokenReason" placeholder="新增損壞原因...">
                            <button type="button" id="addBrokenReason">新增</button>
                        </div>
                        <ul id="brokenReasonList" class="settings-list">
                            ${currentSettings.brokenReason.map(reason => 
                                `<li>${reason} <button type="button" class="remove-btn" data-reason="${reason}">移除</button></li>`
                            ).join('')}
                        </ul>
                    </div>
                </div>

                <!-- 操作按鈕 -->
                <div class="settings-actions">
                    <button type="button" id="saveSettings" class="btn-primary">儲存設定</button>
                    <button type="button" id="resetSettings" class="btn-secondary">重置為預設值</button>
                    <button type="button" id="exportSettings" class="btn-secondary">匯出設定</button>
                    <button type="button" id="importSettings" class="btn-secondary">匯入設定</button>
                    <input type="file" id="importFile" accept=".json" style="display: none;">
                </div>
            </div>
        `;

        this.attachEventListeners();
        this.addStyles();
    }

    /**
     * 附加事件監聽器
     */
    private attachEventListeners(): void {
        if (!this.container) return;

        // 儲存設定
        const saveBtn = this.container.querySelector('#saveSettings') as HTMLButtonElement;
        saveBtn?.addEventListener('click', () => this.saveSettings());

        // 重置設定
        const resetBtn = this.container.querySelector('#resetSettings') as HTMLButtonElement;
        resetBtn?.addEventListener('click', () => this.resetSettings());

        // 匯出設定
        const exportBtn = this.container.querySelector('#exportSettings') as HTMLButtonElement;
        exportBtn?.addEventListener('click', () => this.exportSettings());

        // 匯入設定
        const importBtn = this.container.querySelector('#importSettings') as HTMLButtonElement;
        const importFile = this.container.querySelector('#importFile') as HTMLInputElement;
        importBtn?.addEventListener('click', () => importFile?.click());
        importFile?.addEventListener('change', (e) => this.importSettings(e));

        // 新增位置
        const addLocationBtn = this.container.querySelector('#addLocation') as HTMLButtonElement;
        const newLocationInput = this.container.querySelector('#newLocation') as HTMLInputElement;
        addLocationBtn?.addEventListener('click', () => this.addLocation());
        newLocationInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addLocation();
        });

        // 移除位置
        this.container.querySelectorAll('[data-location]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const location = (e.target as HTMLElement).getAttribute('data-location');
                if (location) this.removeLocation(location);
            });
        });

        // 新增損壞原因
        const addReasonBtn = this.container.querySelector('#addBrokenReason') as HTMLButtonElement;
        const newReasonInput = this.container.querySelector('#newBrokenReason') as HTMLInputElement;
        addReasonBtn?.addEventListener('click', () => this.addBrokenReason());
        newReasonInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.addBrokenReason();
        });

        // 移除損壞原因
        this.container.querySelectorAll('[data-reason]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const reason = (e.target as HTMLElement).getAttribute('data-reason');
                if (reason) this.removeBrokenReason(reason);
            });
        });
    }

    /**
     * 儲存設定
     */
    private saveSettings(): void {
        if (!this.container) return;

        const getValue = (id: string): number => {
            const input = this.container!.querySelector(`#${id}`) as HTMLInputElement;
            return parseFloat(input.value) || 0;
        };

        // 更新所有數值設定
        this.settings.setPackVoltageLimit(getValue('packVoltageLimit'));
        this.settings.setCellVoltage(getValue('cellVoltage'));
        this.settings.setUnbalanceVoltage(getValue('unbalanceVoltage'));
        this.settings.setCycleCountLimit(getValue('cycleCountLimit'));
        this.settings.setTemperatureLimit(getValue('temperatureLimit'));

        // 更新循環計數等級
        for (let i = 1; i <= 6; i++) {
            this.settings.setCycleCountLevel(i as 1|2|3|4|5|6, getValue(`cycleCountLevel${i}`));
        }

        // 更新隔離設定
        this.settings.setIsolateCycle(getValue('isolateCycle'));
        this.settings.setIsolateHealth(getValue('isolateHealth'));
        this.settings.setIsolateYearTime(getValue('isolateYearTime'));

        alert('設定已儲存！');
    }

    /**
     * 重置設定
     */
    private resetSettings(): void {
        if (confirm('確定要重置為預設設定嗎？此操作無法復原。')) {
            this.settings.resetToDefaults();
            if (this.container) {
                this.createSettingsUI(this.container);
            }
            alert('設定已重置為預設值！');
        }
    }

    /**
     * 匯出設定
     */
    private exportSettings(): void {
        const settingsJson = this.settings.exportSettings();
        const blob = new Blob([settingsJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'battery-settings.json';
        a.click();
        URL.revokeObjectURL(url);
    }

    /**
     * 匯入設定
     */
    private importSettings(event: Event): void {
        const input = event.target as HTMLInputElement;
        const file = input.files?.[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const content = e.target?.result as string;
            if (this.settings.importSettings(content)) {
                if (this.container) {
                    this.createSettingsUI(this.container);
                }
                alert('設定匯入成功！');
            } else {
                alert('設定匯入失敗，請檢查檔案格式。');
            }
        };
        reader.readAsText(file);
    }

    /**
     * 新增位置
     */
    private addLocation(): void {
        const input = this.container?.querySelector('#newLocation') as HTMLInputElement;
        const location = input?.value.trim();
        if (location) {
            this.settings.addLocation(location);
            input.value = '';
            this.refreshLocationList();
        }
    }

    /**
     * 移除位置
     */
    private removeLocation(location: string): void {
        this.settings.removeLocation(location);
        this.refreshLocationList();
    }

    /**
     * 新增損壞原因
     */
    private addBrokenReason(): void {
        const input = this.container?.querySelector('#newBrokenReason') as HTMLInputElement;
        const reason = input?.value.trim();
        if (reason) {
            this.settings.addBrokenReason(reason);
            input.value = '';
            this.refreshBrokenReasonList();
        }
    }

    /**
     * 移除損壞原因
     */
    private removeBrokenReason(reason: string): void {
        this.settings.removeBrokenReason(reason);
        this.refreshBrokenReasonList();
    }

    /**
     * 刷新位置列表
     */
    private refreshLocationList(): void {
        const list = this.container?.querySelector('#locationList');
        if (list) {
            const locations = this.settings.locationList;
            list.innerHTML = locations.map(location => 
                `<li>${location} <button type="button" class="remove-btn" data-location="${location}">移除</button></li>`
            ).join('');
            
            // 重新附加事件監聽器
            list.querySelectorAll('[data-location]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const location = (e.target as HTMLElement).getAttribute('data-location');
                    if (location) this.removeLocation(location);
                });
            });
        }
    }

    /**
     * 刷新損壞原因列表
     */
    private refreshBrokenReasonList(): void {
        const list = this.container?.querySelector('#brokenReasonList');
        if (list) {
            const reasons = this.settings.brokenReason;
            list.innerHTML = reasons.map(reason => 
                `<li>${reason} <button type="button" class="remove-btn" data-reason="${reason}">移除</button></li>`
            ).join('');
            
            // 重新附加事件監聽器
            list.querySelectorAll('[data-reason]').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    const reason = (e.target as HTMLElement).getAttribute('data-reason');
                    if (reason) this.removeBrokenReason(reason);
                });
            });
        }
    }

    /**
     * 添加樣式
     */
    private addStyles(): void {
        const style = document.createElement('style');
        style.textContent = `
            .battery-settings-panel {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }

            .settings-section {
                margin-bottom: 30px;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background: #f9f9f9;
            }

            .settings-section h4 {
                margin-top: 0;
                color: #333;
                border-bottom: 2px solid #007bff;
                padding-bottom: 10px;
            }

            .settings-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 15px;
                margin-top: 15px;
            }

            .setting-item {
                display: flex;
                flex-direction: column;
            }

            .setting-item label {
                font-weight: bold;
                margin-bottom: 5px;
                color: #555;
            }

            .setting-item input {
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 14px;
            }

            .list-manager {
                margin-top: 15px;
            }

            .list-input {
                display: flex;
                gap: 10px;
                margin-bottom: 15px;
            }

            .list-input input {
                flex: 1;
                padding: 8px;
                border: 1px solid #ccc;
                border-radius: 4px;
            }

            .list-input button {
                padding: 8px 16px;
                background: #007bff;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
            }

            .settings-list {
                list-style: none;
                padding: 0;
                max-height: 200px;
                overflow-y: auto;
                border: 1px solid #ddd;
                border-radius: 4px;
                background: white;
            }

            .settings-list li {
                padding: 10px;
                border-bottom: 1px solid #eee;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .remove-btn {
                background: #dc3545;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 3px;
                cursor: pointer;
                font-size: 12px;
            }

            .settings-actions {
                display: flex;
                gap: 10px;
                justify-content: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 2px solid #ddd;
            }

            .btn-primary {
                background: #007bff;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
                font-weight: bold;
            }

            .btn-secondary {
                background: #6c757d;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 16px;
            }

            .btn-primary:hover, .btn-secondary:hover {
                opacity: 0.9;
            }
        `;
        document.head.appendChild(style);
    }
}
