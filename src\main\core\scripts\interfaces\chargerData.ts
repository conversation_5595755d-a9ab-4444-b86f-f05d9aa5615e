enum DevicePidVid
{
    UPower22    = 0x03EB0022,
    UPower22GD  = 0x03EB4732,
    UPower43    = 0x04830025,
    UPower62    = 0x03EB2402,
    UPower62Wifi= 0x03EB4736,
    UPowerM62   = 0x04830024,
    OPM_P09C    = 0x03EB0023,
    OPM_P14C    = 0x03EB0014,
}

export interface ChargerData {
    projectName: string,  // 用來對應 C 的 const char*
    firmwareVersion: string,
    serialNumber: string,
    acStatus: string,
}

export class chargerDataHelper {
    static getDeviceModelName(pidVid: number): string {
        switch(pidVid) {
            case DevicePidVid.UPower22:
                return "UPower 22";
            case DevicePidVid.UPower22GD:
                return "UPower 22GD";
            case DevicePidVid.UPower43:
                return "UPower 43";
            case DevicePidVid.UPower62:
                return "UPower 62";
            case DevicePidVid.UPower62Wifi:
                return "UPower 62 Wifi";
            case DevicePidVid.UPowerM62:
                return "UPower M62";
            case DevicePidVid.OPM_P09C:
                return "OPM P09C";
            case DevicePidVid.OPM_P14C:
                return "OPM P14C";
            default:
                return "未知型號";
        }
    }
}

export { DevicePidVid };