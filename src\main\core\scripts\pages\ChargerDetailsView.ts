import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';

// const tbxProduct = document.getElementById("product") as HTMLElement;
// const tbxFirmwareVersion = document.getElementById("firmware-version") as HTMLInputElement;
// const tbxSerialNumber = document.getElementById("serial-number") as HTMLInputElement;

console.log("ChargeDetailsView.ts 已載入");

let tbxProduct: HTMLElement | null;
let tbxFirmwareVersion: HTMLElement | null;
let tbxSerialNumber: HTMLElement | null;

async function delay(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}


async function Initial() {
  const vList = [0x0000FFFF, 0xFFFF0000, 0x03EB4736];
  for (const product of vList) {
    if (await invoke("Initial", { product })) {
      await delay(500);
      tbxProduct!.textContent = await invoke("Charger_GetName");
      tbxFirmwareVersion!.textContent = await invoke("Charger_GetVersion");
      tbxSerialNumber!.textContent = await invoke("Charger_GetSerialNumber");
      //await invoke("TEST");
      return;
    }
  }
  tbxProduct!.textContent = "NA";
  tbxFirmwareVersion!.textContent = "NA";
  tbxSerialNumber!.textContent = "NA";
}

export async function upgrade(){
  // 儀表板頁面的初始化邏輯
  tbxProduct = document.getElementById("product");
  tbxFirmwareVersion = document.getElementById("firmware-version");
  tbxSerialNumber = document.getElementById("serial-number");
  try {
    if (await invoke("LOAD_SDK")) {
      listen("USB_ChangedEvent", async () => {
        Initial();
      });
      listen<number>('Upgrade_ProgressUpdatedEvent', (event) => {
        tbxProduct!.textContent = event.payload.toString() + "%";
      });
      Initial();
    }
  }
  catch (error) {
    console.error("Failed to load library:", error);
  }
}

// (async function() {
  
// })();
