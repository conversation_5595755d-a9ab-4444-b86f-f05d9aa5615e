[package]
name = "package"
version = "0.25.318"
edition = "2021"
description = ""
authors = ["JingWen"]

build = "src/build.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[[bin]]
name = "UPowerApplication" # UPowerApplication.exe
path = "src/main/core/MainActivity.rs"

[build-dependencies]
tauri-build = { version = "2", features = [] }
#tauri = { version = "2", features = ["devtools"] }

[dependencies]
chrono = { version = "0.4", features = ["serde"] }
lazy_static = "1.5.0"
libloading = "0.8"
num_enum = "0.7"
once_cell = "1.19"
rand = "0.8"
rusb = "0.9"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri = { version = "2", features = ["protocol-asset", "devtools"] }
tauri-plugin-opener = "2"
tauri-plugin-http = "2.0.0"
tauri-plugin-fs = "2"

#[profile.release.package.wry]
#debug = true
#debug-assertions = true
