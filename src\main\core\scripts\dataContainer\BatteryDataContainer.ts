import { invoke } from "@tauri-apps/api/core";
// import { listen } from '@tauri-apps/api/event';
import { getCycleCountLevel1, getCycleCountLevel2, getCycleCountLevel3, getCycleCountLevel4, getCycleCountLevel5, getCycleCountLevel6 } from '../settings';
import { BatteryData, BatteryType } from "../interfaces/batteryData";

export class BatteryDataContainer {
  /**
   * 根據序列號獲取電池類型
   * @param serialNumber 電池序列號
   * @returns 電池類型
   */
  // static getBatteryType(serialNumber: string): BatteryType {
  //     if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
  //         return BatteryType.Standard;//1
  //     } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
  //         return BatteryType.XL;//2
  //     } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
  //         return BatteryType.XXL;//3
  //     } else if (serialNumber.toUpperCase().includes("OMD")) {
  //         return BatteryType.Mini;
  //     } else {
  //         return BatteryType.Unknown;
  //     }
  // }
  static getBatteryType(batteryPack: number): BatteryType {
    // 0:Unknown 1: Mini, 2: Standard, 3: XL, 4: XXL
    
      if (batteryPack === 1) {
          return BatteryType.Standard;//1
      } else if (batteryPack === 2) {
          return BatteryType.XL;//2
      } else if (batteryPack === 3) {
          return BatteryType.XXL;//3
      } else if (batteryPack === 4) {
          return BatteryType.Mini;
      } else {
          return BatteryType.Unknown;
      }
  }

  /**
   * 根據序列號獲取電池類型文字
   * @param serialNumber 電池序列號
   * @returns 電池類型文字
   */
  // static getBatteryTypeText(serialNumber: string): string {
  //   if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
  //     return "Standard";
  //   } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
  //     return "XL";
  //   } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
  //     return "XXL";
  //   } else if (serialNumber.toUpperCase().includes("OMD")) {
  //     return "Mini";
  //   } else {
  //     return "Unknown";
  //   }
  // }
  static getBatteryTypeText(batteryType: BatteryType): string {
      if (batteryType === BatteryType.Standard) {
        return "Standard";
      } else if (batteryType === BatteryType.XL) {
        return "XL";
      } else if (batteryType === BatteryType.XXL) {
        return "XXL";
      } else if (batteryType === BatteryType.Mini) {
        return "Mini";
      } else {
        return "Unknown";
      }
    }

  /**
   * 計算電池健康狀況
   * @param m_FullChargeCapacity 完全充電容量
   * @param m_DesignCapacity 設計容量
   * @returns 電池健康狀況百分比
   */
  static getBatteryHealth(m_FullChargeCapacity: number, m_DesignCapacity: number): number
  {
      try
      {
          const fHealth = (m_FullChargeCapacity / m_DesignCapacity) * 100;

          return (fHealth > 100) ? 100 : fHealth;
      }
      catch (error)
      {
          console.error("計算電池健康狀況時發生錯誤:", error);
          return 0;
      }
  }

  /**
   * 根據電池數據計算循環次數等級
   * @param batteryData 電池數據
   * @returns 循環次數等級
   */
  static getCycleCountLevel(batteryData: BatteryData): number
  {
      /*
          CycleCountLevel1 = 89.55;
          CycleCountLevel2 = 79.80;
          CycleCountLevel3 = 78.56;
          CycleCountLevel4 = 68.64;
          CycleCountLevel5 = 48.64;
          CycleCountLevel6 = 33.00;
      */
      let fLevel = 0;

      try
      {
          if (batteryData != null)
          {
              //if (batteryData.cycle >= getCycleCountLevel(1)) return 1;

              if (batteryData.cycle >= 0 && batteryData.cycle <= 100)
                  fLevel = getCycleCountLevel1(); // 89.55; //CycleCountLevel1
              else if (batteryData.cycle > 101 && batteryData.cycle <= 200)
                  fLevel = getCycleCountLevel2(); //79.80; //CycleCountLevel2
              else if (batteryData.cycle > 201 && batteryData.cycle <= 300)
                  fLevel = getCycleCountLevel3(); //78.56; //CycleCountLevel3
              else if (batteryData.cycle > 301 && batteryData.cycle <= 400)
                  fLevel = getCycleCountLevel4(); //68.64; //CycleCountLevel4
              else if (batteryData.cycle > 401 && batteryData.cycle <= 500)
                  fLevel = getCycleCountLevel5(); //48.64; //CycleCountLevel5
              else if (batteryData.cycle > 501)
                  fLevel = getCycleCountLevel6(); //3.00; //CycleCountLevel6
          }

      }
      catch (error)
      {
          console.error("計算循環次數等級時發生錯誤:", error);
          return 0;
      }
      return fLevel;
  }

  static getMinCellVoltage(batData: BatteryData): number {
      let batCellVoltages = [];// = [batData.cellVoltage_1 ];
      if (batData.cellVoltage_1) batCellVoltages.push(batData.cellVoltage_1);
      if (batData.cellVoltage_2) batCellVoltages.push(batData.cellVoltage_2);
      if (batData.cellVoltage_3) batCellVoltages.push(batData.cellVoltage_3);
      if (batData.cellVoltage_3 > 0 && batData.cellVoltage_3 != null) {
          if (batData.cellVoltage_4) batCellVoltages.push(batData.cellVoltage_4);
      }
      
      return Math.min(...batCellVoltages);
  }

  static getMaxCellVoltage(batData: BatteryData): number {
      let batCellVoltages = [];// = [batData.cellVoltage_1 ];
      if (batData.cellVoltage_1) batCellVoltages.push(batData.cellVoltage_1);
      if (batData.cellVoltage_2) batCellVoltages.push(batData.cellVoltage_2);
      if (batData.cellVoltage_3) batCellVoltages.push(batData.cellVoltage_3);
      if (batData.cellVoltage_3 > 0 && batData.cellVoltage_3 != null) {
          if (batData.cellVoltage_4) batCellVoltages.push(batData.cellVoltage_4);
      }
      
      return Math.max(...batCellVoltages);
  }

  // 查看電池是否正確取得資訊
  // private static isBatteryComplete(m_fullyChargedCapacity: number, m_remainingCapacity: number, m_cellVoltage_1: number): boolean
  // {
  //     try
  //     {
  //     if(m_fullyChargedCapacity !== 0 && m_remainingCapacity !== 0 && m_cellVoltage_1 > 0 ){
  //         return true;  
  //     }
  //     }
  //     catch (error)
  //     {
  //         console.error("計算電池健康狀況時發生錯誤:", error);
  //         return false;
  //     }
  //     return false;
  // }

  // 查看電池是否正確取得資訊
  private static isBatteryComplete(batteryData: BatteryData): boolean
  {
      try
      {
          if(batteryData.fullyChargedCapacity !== 0 && batteryData.remainingCapacity !== 0 
              && batteryData.designCapacity > 0 && batteryData.temperature > 0 && batteryData.voltage > 0 ){
              return true;  
          }
      }
      catch (error)
      {
          console.error("計算電池健康狀況時發生錯誤:", error);
          return false;
      }
      return false;
  }

  static async getBatteryData(): Promise<BatteryData[]> {
    try {
        const batteryDataList: BatteryData[] = await invoke<BatteryData[]>("Charger_GetBatteries");
        if (batteryDataList.length === 0) {
            console.log("No battery data found");
        }
        // 根據指定順序創建電池卡片
        batteryDataList.forEach(batteryData => {
            batteryData.isComplete = false;
            if (batteryData && batteryData?.sn !== "") {
                if (this.isBatteryComplete(batteryData)) {
                    //batteryData.fullyChargedCapacity, batteryData.remainingCapacity, batteryData.cellVoltage_1, batteryData.temperature, batteryData.designCapacity, batteryData.voltage
                    
                    // console.log("Battery data is complete");
                    batteryData.isComplete = true;
                    batteryData.type = this.getBatteryType(batteryData.pack);
                    batteryData.health = this.getBatteryHealth(batteryData?.fullyChargedCapacity ?? 0, batteryData?.designCapacity ?? 0)
                }
            }
            
  
            //const batteryCard = createBatteryCard(index, batteryData);
            //batteryGrid.appendChild(batteryCard);
        });
        
        return batteryDataList;
    }
    catch (error) {
      console.error("getBatteryData error:", error);
      return [];
    }
      
  }
  
}

// // 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
// function getBatteryType(serialNumber: string): BatteryType {
//     if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
//       return BatteryType.Standard; // "Standard";
//     } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
//       return BatteryType.XL; // "XL";
//     } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
//       return BatteryType.XXL; // "XXL";
//     } else if (serialNumber.toUpperCase().includes("OMD")) {
//       return BatteryType.Mini; // "Mini";
//     } else {
//       return BatteryType.Unknown; // "Unknown";
//     }
// }

// function getBatteryTypeText(serialNumber: string): string {
//   if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
//     return "Standard";
//   } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
//     return "XL";
//   } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
//     return "XXL";
//   } else if (serialNumber.toUpperCase().includes("OMD")) {
//     return "Mini";
//   } else {
//     return "Unknown";
//   }
// }

// export {
//     getBatteryType,
//     getBatteryTypeText,
// }