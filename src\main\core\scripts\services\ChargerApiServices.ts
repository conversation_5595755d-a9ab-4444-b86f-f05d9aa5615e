import { ChargerData as Charger } from "../interfaces/chargerData";
import { fetch } from '@tauri-apps/plugin-http'
// API 服務
export class ChargerApiService {
  private apiUrl = 'http://localhost:8080/api';

  // 獲取所有電池座
  async getAllCharger(): Promise<Charger[]> {     
    try {
      const response = await fetch(`${this.apiUrl}/chargers`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("獲取 chargers 列表失敗:", error);
      return [];
    }
  }

  // 獲取單個電池座
  async getCharger(id: string): Promise<Charger | null> {
    try {
      const response = await fetch(`${this.apiUrl}/chargers/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`獲取 chargers ${id} 失敗:`, error);
      return null;
    }
  }

  // 新增電池座
  async addCharger(charger: Omit<Charger, "id">): Promise<Charger | null> {
    try {
      const response = await fetch(`${this.apiUrl}/chargers`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(charger)
      });
      console.log("response", response);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("新增 chargers 失敗:", error);
      return null;
    }
  }

  // 修改電池座
  async updateCharger(id: string, charger: Partial<Charger>): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/chargers/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(charger)
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`修改 chargers ${id} 失敗:`, error);
      return false;
    }
  }

  // 刪除電池座
  async deleteChargers(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/chargers/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`刪除 charger ${id} 失敗:`, error);
      return false;
    }
  }
}