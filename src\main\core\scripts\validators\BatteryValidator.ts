import { BatteryData, BatteryType } from "../interfaces/batteryData";
import { BatteryDataContainer } from "../dataContainer/BatteryDataContainer";
import { getPackVoltageLimit, getCycleCountLimit, getTemperatureLimit, getIsolateHealth, getCellVoltage, getUnbalanceVoltage } from '../settings';

/**
 * 電池損壞資訊介面
 */
export interface BatteryDamageInfo {
    [batteryId: string]: boolean;
}

/**
 * 電池驗證類
 * 提供電池相關的驗證功能
 */
export class BatteryValidator {
    /**
     * 驗證電池序列號是否有效
     * 
     * 驗證規則：
     * 1. XL 類型：序列號以 "9789B" 開頭
     * 2. Standard 類型：序列號以 "17500" 開頭
     * 3. 格式必須符合正則表達式：^[a-zA-Z0-9]*-[a-zA-Z0-9]*$
     * 4. 可選擇是否排除特定 SN (1803, 1804)
     * 
     * @param serial 電池序列號
     * @param check1803to04 是否檢查並排除 SN 為 1803 和 1804，預設為 true
     * @returns 序列號是否有效
     */
    public static isSerialNumberValid(serial: string, check1803to04: boolean = true): boolean {
        let ret = false;
        
        // 正則表達式：檢查格式是否為 字母數字-字母數字
        const regex = /^[a-zA-Z0-9]*-[a-zA-Z0-9]*$/;

        // XL 類型檢查
        if (serial.startsWith("9789B")) {
            ret = true;
        }
        // Standard 類型檢查
        else if (serial.startsWith("17500")) {
            ret = true;
        }

        // 檢查是否符合正則表達式格式
        if (regex.test(serial)) {
            if (check1803to04) {
                // 找到 '-' 的位置
                const dashIndex = serial.indexOf('-');
                if (dashIndex !== -1 && dashIndex + 5 <= serial.length) {
                    // 提取 '-' 後的 4 個字符作為日期
                    const dateInSN = serial.substring(dashIndex + 1, dashIndex + 5);
                    
                    // 如果 SN 不是 1803 或 1804，則有效
                    if (dateInSN !== "1803" && dateInSN !== "1804") {
                        ret = true;
                    } else {
                        ret = false;
                    }
                } else {
                    // 如果格式不正確（沒有足夠的字符），則無效
                    ret = false;
                }
            } else {
                // 不檢查特定日期，只要格式正確就有效
                ret = true;
            }
        } else {
            // 格式不符合正則表達式，無效
            ret = false;
        }

        return ret;
    }

    /**
     * 驗證序列號格式是否正確（不檢查前綴）
     * @param serial 電池序列號
     * @returns 格式是否正確
     */
    public static isSerialFormatValid(serial: string): boolean {
        const regex = /^[a-zA-Z0-9]*-[a-zA-Z0-9]*$/;
        return regex.test(serial);
    }

    /**
     * 從序列號中提取日期部分
     * @param serial 電池序列號
     * @returns 日期字符串，如果無法提取則返回空字符串
     */
    public static extractDateFromSerial(serial: string): string {
        const dashIndex = serial.indexOf('-');
        if (dashIndex !== -1 && dashIndex + 5 <= serial.length) {
            return serial.substring(dashIndex + 1, dashIndex + 5);
        }
        return "";
    }

    /**
     * 檢查是否為被排除的 SN (1803, 1804)
     * @param snStr SN
     * @returns 是否為被排除
     */
    public static isExcludedPrefix(snStr: string): boolean {
        return snStr === "1803" || snStr === "1804";
    }

    /**
     * 電池損壞狀態管理常數
     */
    private static readonly DAMAGE_INFO_KEY = 'batteryDamageInfo';

    /**
     * 從 localStorage 讀取電池損壞狀態資訊
     * @returns 電池損壞狀態資訊物件
     */
    public static getBatteryDamageInfo(): { [batteryId: string]: boolean } {
        try {
            const stored = localStorage.getItem(this.DAMAGE_INFO_KEY);
            return stored ? JSON.parse(stored) : {};
        } catch (error) {
            console.error('讀取電池損壞狀態資訊失敗:', error);
            return {};
        }
    }

    /**
     * 儲存電池損壞狀態資訊到 localStorage
     * @param damageInfo 電池損壞狀態資訊
     */
    public static saveBatteryDamageInfo(damageInfo: { [batteryId: string]: boolean }): void {
        try {
            localStorage.setItem(this.DAMAGE_INFO_KEY, JSON.stringify(damageInfo));
        } catch (error) {
            console.error('儲存電池損壞狀態資訊失敗:', error);
        }
    }

    /**
     * 設定特定電池的損壞狀態
     * @param batteryId 電池 ID
     * @param isDamaged 是否損壞
     */
    public static setBatteryDamage(batteryId: string, isDamaged: boolean): void {
        const damageInfo = this.getBatteryDamageInfo();
        damageInfo[batteryId] = isDamaged;
        this.saveBatteryDamageInfo(damageInfo);
    }

    /**
     * 獲取特定電池的損壞狀態
     * @param batteryId 電池 ID
     * @returns 是否損壞
     */
    private static getBatteryDamage(batteryId: string): boolean {
        const damageInfo = this.getBatteryDamageInfo();
        return damageInfo[batteryId] || false;
    }

    /**
     * 驗證電池外觀是否損壞（基於 localStorage 儲存的狀態）
     * @param batteryId 電池 ID
     * @returns 是否損壞
     */
    public static isAppearanceBroken(batteryId: string): boolean {
        return this.getBatteryDamage(batteryId);
    }

    /**
     * 批量獲取多個電池的損壞狀態
     * @param batteryIds 電池 ID 陣列
     * @returns 電池損壞狀態對應表
     */
    public static getBatteryDamageStatus(batteryIds: string[]): { [batteryId: string]: boolean } {
        const damageInfo = this.getBatteryDamageInfo();
        const result: { [batteryId: string]: boolean } = {};

        batteryIds.forEach(id => {
            result[id] = damageInfo[id] || false;
        });

        return result;
    }

    /**
     * 清除所有電池的損壞狀態
     */
    public static clearAllBatteryDamageInfo(): void {
        try {
            localStorage.removeItem(this.DAMAGE_INFO_KEY);
        } catch (error) {
            console.error('清除電池損壞狀態資訊失敗:', error);
        }
    }

    // /**
    //  * 獲取所有損壞的電池 ID 列表
    //  * @returns 損壞電池的 ID 陣列
    //  */
    // public static getDamagedBatteryIds(): string[] {
    //     const damageInfo = this.getBatteryDamageInfo();
    //     return Object.keys(damageInfo).filter(id => damageInfo[id] === true);
    // }

    /**
     * 基於全域設定檢測電池電壓是否正常
     * @param voltage 電池電壓 (mV)
     * @returns 是否正常
     */
    // public static isVoltageNormal(voltage: number): boolean {
    //     const limit = getPackVoltageLimit();
    //     return voltage > 0 && voltage <= limit;
    // }

    // /**
    //  * 基於全域設定檢測電池循環次數是否超限
    //  * @param cycleCount 循環次數
    //  * @returns 是否超限
    //  */
    // public static isCycleCountExceeded(cycleCount: number): boolean {
    //     const limit = getCycleCountLimit();
    //     return cycleCount >= limit;
    // }

    // /**
    //  * 基於全域設定檢測電池溫度是否正常
    //  * @param temperature 溫度 (°C)
    //  * @returns 是否正常
    //  */
    // public static isTemperatureNormal(temperature: number): boolean {
    //     const limit = getTemperatureLimit();
    //     return temperature >= -20 && temperature <= limit;
    // }

    // /**
    //  * 基於全域設定檢測電池單體電壓是否正常
    //  * @param cellVoltage 單體電壓 (mV)
    //  * @returns 是否正常
    //  */
    // public static isCellVoltageNormal(cellVoltage: number): boolean {
    //     const limit = getCellVoltage();
    //     return cellVoltage > 0 && cellVoltage <= limit;
    // }

    // /**
    //  * 基於全域設定檢測電池不平衡電壓是否正常
    //  * @param unbalanceVoltage 不平衡電壓 (mV)
    //  * @returns 是否正常
    //  */
    // public static isUnbalanceVoltageNormal(unbalanceVoltage: number): boolean {
    //     const limit = getUnbalanceVoltage();
    //     return unbalanceVoltage <= limit;
    // }

    // /**
    //  * 基於全域設定獲取電池健康度等級
    //  * @param healthPercentage 健康度百分比
    //  * @returns 健康度等級 (1-6, 1為最佳)
    //  */
    // public static getBatteryHealthLevel(healthPercentage: number): number {
    //     if (healthPercentage >= getCycleCountLevel(1)) return 1;
    //     if (healthPercentage >= getCycleCountLevel(2)) return 2;
    //     if (healthPercentage >= getCycleCountLevel(3)) return 3;
    //     if (healthPercentage >= getCycleCountLevel(4)) return 4;
    //     if (healthPercentage >= getCycleCountLevel(5)) return 5;
    //     if (healthPercentage >= getCycleCountLevel(6)) return 6;
    //     return 7; // 低於所有等級
    // }

    // /**
    //  * 基於全域設定檢查電池是否需要隔離
    //  * @param batteryData 電池數據
    //  * @returns 隔離檢查結果
    //  */
    // public static checkBatteryIsolation(batteryData: any): {
    //     shouldIsolate: boolean;
    //     reasons: string[];
    // } {
    //     const settings = getBatterySettings();
    //     const reasons: string[] = [];
    //     let shouldIsolate = false;

    //     // 檢查循環次數
    //     if (batteryData.cycle >= settings.isolateCycle) {
    //         reasons.push(`Cycle count (${batteryData.cycle}) exceeds isolation limit (${settings.isolateCycle})`);
    //         shouldIsolate = true;
    //     }

    //     // 檢查健康度
    //     const healthPercentage = (batteryData.fullyChargedCapacity / batteryData.designCapacity) * 100;
    //     if (healthPercentage <= settings.isolateHealth) {
    //         reasons.push(`Health (${healthPercentage.toFixed(1)}%) below isolation threshold (${settings.isolateHealth}%)`);
    //         shouldIsolate = true;
    //     }

    //     // 檢查年限（需要製造日期）
    //     if (batteryData.manufactureDate) {
    //         const manufactureYear = new Date(batteryData.manufactureDate).getFullYear();
    //         const currentYear = new Date().getFullYear();
    //         const age = currentYear - manufactureYear;

    //         if (age >= settings.isolateYearTime) {
    //             reasons.push(`Battery age (${age} years) exceeds isolation limit (${settings.isolateYearTime} years)`);
    //             shouldIsolate = true;
    //         }
    //     }

    //     return { shouldIsolate, reasons };
    // }

    // /**
    //  * 綜合電池檢測（使用全域設定）
    //  * @param batteryData 電池數據
    //  * @returns 檢測結果
    //  */
    // public static comprehensiveBatteryCheck(batteryData: BatteryData): {
    //     isValid: boolean;
    //     serialValid: boolean;
    //     voltageNormal: boolean;
    //     temperatureNormal: boolean;
    //     cycleCountOk: boolean;
    //     healthLevel: number;
    //     shouldIsolate: boolean;
    //     issues: string[];
    //     isolationReasons: string[];
    // } {
    //     const issues: string[] = [];

    //     // 序列號檢測
    //     const serialValid = this.isSerialNumberValid(batteryData.sn);
    //     if (!serialValid) {
    //         issues.push("Invalid serial number");
    //     }

    //     // 電壓檢測
    //     console.log('電壓檢測 V: ' + batteryData.voltage);
    //     const voltageNormal = this.isPackVoltageLowerThanLimit(batteryData.voltage, batteryDataHelper.getBatteryType(batteryData.sn));
    //     if (!voltageNormal) {
    //         issues.push(`Voltage (${batteryData.voltage}mV) exceeds limit`);
    //     }

    //     // 溫度檢測
    //     const temperatureNormal = this.isTemperatureNormal(batteryData.temperature);
    //     if (!temperatureNormal) {
    //         issues.push(`Temperature (${batteryData.temperature}°C) out of range`);
    //     }

    //     // 循環次數檢測
    //     const cycleCountOk = !this.isCycleCountExceeded(batteryData.cycle);
    //     if (!cycleCountOk) {
    //         issues.push(`Cycle count (${batteryData.cycle}) exceeds limit`);
    //     }

    //     // 健康度等級
    //     const healthPercentage = (batteryData.fullyChargedCapacity / batteryData.designCapacity) * 100;
    //     const healthLevel = this.getBatteryHealthLevel(healthPercentage);

    //     // 隔離檢查
    //     const isolationCheck = this.checkBatteryIsolation(batteryData);

    //     return {
    //         isValid: serialValid && voltageNormal && temperatureNormal && cycleCountOk,
    //         serialValid,
    //         voltageNormal,
    //         temperatureNormal,
    //         cycleCountOk,
    //         healthLevel,
    //         shouldIsolate: isolationCheck.shouldIsolate,
    //         issues,
    //         isolationReasons: isolationCheck.reasons
    //     };
    // }


    // /**
    //  * 檢查電池電壓範圍
    //  * @param batteryDataList 電池數據列表
    //  * @returns 損壞檢測報告
    //  */
    // public static checkBatteryPackVoltageLowerThanLimit(batteryDataList: any[]): {
    //     totalBatteries: number;
    //     damagedBatteries: number;
    //     healthyBatteries: number;
    //     damageRate: number;
    //     damagedBatteryDetails: Array<{
    //         batteryId: number;
    //         serial: string;
    //         batteryType: string;
    //         isDamaged: boolean;
    //     }>;
    // } {
    //     const batteryIds = batteryDataList.map(battery => battery.id.toString());
    //     const damageStatus = this.getBatteryDamageStatus(batteryIds);
    
    //     let damagedCount = 0;
    //     const damagedBatteryDetails = batteryDataList.map(battery => {
    //         const isDamaged = damageStatus[battery.id.toString()] || false;
    //         if (isDamaged) damagedCount++;
    
    //         return {
    //             batteryId: battery.id,
    //             serial: battery.sn,
    //             batteryType: batteryDataHelper.getBatteryTypeText(battery.sn),
    //             isDamaged
    //         };
    //     });
    
    //     return {
    //         totalBatteries: batteryDataList.length,
    //         damagedBatteries: damagedCount,
    //         healthyBatteries: batteryDataList.length - damagedCount,
    //         damageRate: batteryDataList.length > 0 ? (damagedCount / batteryDataList.length) * 100 : 0,
    //         damagedBatteryDetails
    //     };
    // }

    /**
     * 檢查電池 Voltage 是否低於限制
     * @param voltage 電池 Voltage
     * @param batteryType 電池類型
     * @returns 
     */
    public static isVoltageLowerThanLimit(voltage: number, batteryType: BatteryType): boolean {
        console.log('檢查 PackVoltage:', voltage, '電池類型:', batteryType);
        // 獲取電池設定
        if(batteryType === BatteryType.XL || batteryType === BatteryType.Standard) {
            // 檢查 PackVoltage 是否小於 6000 mV
            if (typeof voltage !== 'number' || isNaN(voltage)) {
                console.error('無效的 PackVoltage 值:', voltage);
                return true;
            }
            //#if !Venus
            return voltage < getPackVoltageLimit(); 
        }
        else{
            return false;
        }
    }

    /**
     * 檢查電池健康值是否低於限制
     * @param batHealth 電池健康值
     * @returns 是否健康
     */
    public static isHealthLowerThanLimit(batHealth: number): boolean {
        if (batHealth < getIsolateHealth()) {
            console.error('不健康的 Health 值:', batHealth);
            return true;
        }
        return false;
    }

    /**
     * 檢查電池單體電壓是否低於限制
     * @param cellVoltage 電池單體電壓
     * @returns 是否健康
     */
    public static IsCellVoltageLowerThanLimit(cellVoltage: number): boolean {
        if (cellVoltage < getCellVoltage()) {
            console.error('不健康的 CellVoltage 值:', cellVoltage);
            return true;
        }
        return false;
    }
    
    /**
     * 檢查 PF Flag 是否被觸發
     * @param pfFlag PF Flag 狀態
     * @returns 是否已觸發
     */
    public static IsPfFlagRaised(pfFlag: boolean): boolean {
        if (pfFlag) {
            console.error('PF Flag 已被觸發');
            return true;
        }
        return false;
    }

    /**
     * 檢查循環次數是否超出限制
     * @param cycleCount 循環次數
     * @returns 是否超出限制
     */
    public static IsCycleCountOverThanLimit(cycleCount: number): boolean {
        if (cycleCount > getCycleCountLimit()) {
            console.error('Cycle Count 已超出限制');
            return true;
        }
        return false;
    }
    
    /**
     * 檢查電池是否不平衡
     * @param batData 電池數據
     * @returns 是否不平衡
     */
    public static IsBatteryUnbalance(batData: BatteryData): [boolean, number] {
        let maxCellVoltage = BatteryDataContainer.getMaxCellVoltage(batData);
        let minCellVoltage = BatteryDataContainer.getMinCellVoltage(batData);

        console.log('MaxCellVoltage:', maxCellVoltage);
        console.log('MinCellVoltage:', minCellVoltage);
        const voltageDiff = Math.abs(maxCellVoltage - minCellVoltage);
        console.log('voltageDiff:', voltageDiff);
        if (voltageDiff > getUnbalanceVoltage()) {
            console.error('電池不平衡');
            return [true, voltageDiff];
        }
        return [false, voltageDiff];
    }

    public static IsTemperatureValid(temperature: number): boolean {
        const limit = getTemperatureLimit();
        if (temperature > 0 && temperature < limit) {
            return true;
        }
        console.error('溫度超出範圍');
        return false;
    }

    public static VerifyBatteryData(batteryData: BatteryData): {
      isValid: boolean;
      batteryClass: string;
      errorIcons: Array<{
        type: string;
        icon: string;
        message: string;
      }>;
      errorDescription: string;
    } {
      const errorIcons: Array<{ type: string; icon: string; message: string; }> = [];
      let errorDescription = "";
      let isValid = true;
      // let batteryClass = 'battery-normal-image';
      let batteryClass = '';
    
      // 檢查電池外觀是否有毀損
      if(BatteryValidator.isAppearanceBroken(batteryData.sn)){
        isValid = false;
        // batteryClass = 'battery-broken-image';
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[Appearance Broke]";
        errorIcons.push({
          type: 'appearance',
          icon: '💥',
          message: 'Battery Appearance Damaged'
        });
      }
    
      // 檢查電池序列號
      if(!BatteryValidator.isSerialNumberValid(batteryData.sn)){
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[SN Fail]";
        errorIcons.push({
          type: 'serial',
          icon: '🔢',
          message: 'Invalid Serial Number'
        });
      }
    
      // 檢查電池電壓
    //   console.log('電壓檢測 V: ' + batteryData.voltage);
      if(BatteryValidator.isVoltageLowerThanLimit(batteryData.voltage, BatteryDataContainer.getBatteryType(batteryData.pack))) {
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[PackVoltage Fail]";
        errorIcons.push({
          type: 'packVoltage',
          icon: '⚡',
          message: 'Pack Voltage Too Low'
        });
      }
    
      // CheckHealth
      if(BatteryValidator.isHealthLowerThanLimit(BatteryDataContainer.getBatteryHealth(batteryData.fullyChargedCapacity, batteryData.designCapacity))) {
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[Health Fail]";
        errorIcons.push({
          type: 'health',
          icon: '💔',
          message: 'Battery Health Too Low'
        });
      }
      // CellVoltage
      const cellVoltages = [
        { voltage: batteryData.cellVoltage_1, name: 'Cell 1' },
        { voltage: batteryData.cellVoltage_2, name: 'Cell 2' },
        { voltage: batteryData.cellVoltage_3, name: 'Cell 3' },
        { voltage: batteryData.cellVoltage_4, name: 'Cell 4' }
      ];
    
      cellVoltages.forEach((cell, index) => {
        if (cell.voltage && BatteryValidator.IsCellVoltageLowerThanLimit(cell.voltage)) {
          isValid = false;
          //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
          errorDescription += `[CellVolt${index + 1} Fail]`;
          errorIcons.push({
            type: `cellVolt${index + 1}`,
            icon: '🔋',
            message: `${cell.name} Voltage Too Low`
          });
        }
      });
    
      // CheckPFFlag
      if(BatteryDataContainer.getBatteryType(batteryData.pack) === BatteryType.XL ||
      BatteryDataContainer.getBatteryType(batteryData.pack) === BatteryType.Standard ||
      BatteryDataContainer.getBatteryType(batteryData.pack) === BatteryType.Mini){
    
          if(BatteryValidator.IsPfFlagRaised(batteryData.diagParamPfFlag)){
            isValid = false;
            //batteryClass = 'battery-low-image';
            batteryClass = 'verify-error';
            errorDescription += "[PF Fail]";
            errorIcons.push({
              type: 'pfFlag',
              icon: '🚨',
              message: 'PF Flag Raised'
            });
          }
      }
    
      // CheckCycleCount
      if(BatteryValidator.IsCycleCountOverThanLimit(batteryData.cycle)){
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[CycleCount Fail]";
        errorIcons.push({
          type: 'cycleCount',
          icon: '🔄',
          message: 'Cycle Count Exceeded'
        });
      }
    
      // CheckUnbalance
      let [isUnbalance, voltDiff] = BatteryValidator.IsBatteryUnbalance(batteryData);
      if(isUnbalance){
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[CellVolt Unbalance。(voltDiff: " + voltDiff.toFixed(2) + ")]";
        errorIcons.push({
          type: 'unbalance',
          icon: '⚖️',
          message: `Cell Voltage Unbalance (${voltDiff.toFixed(2)}mV)`
        });
      }
    
      // CheckTemperature
      if(!BatteryValidator.IsTemperatureValid(batteryData.temperature)){
        isValid = false;
        //batteryClass = 'battery-low-image';
        batteryClass = 'verify-error';
        errorDescription += "[Temperature Fail]";
        errorIcons.push({
          type: 'temperature',
          icon: '🌡️',
          message: 'Temperature Too High'
        });
      }
    
      
      return {
        isValid,
        batteryClass,
        errorIcons,
        errorDescription
      };
    }




}
