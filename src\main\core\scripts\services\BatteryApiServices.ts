import { BatteryData as Battery, BatteryDataOut } from "../interfaces/batteryData";
import { fetch } from '@tauri-apps/plugin-http'
// API 服務
export class BatteryApiService {
  private apiUrl = 'http://localhost:8080/api';

  // 獲取所有電池
  async getAllBatteries(): Promise<Battery[]> {     
    try {
      const response = await fetch(`${this.apiUrl}/batteries`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("獲取電池列表失敗:", error);
      return [];
    }
  }

  // 獲取單個電池
  async getBattery(id: string): Promise<Battery | null> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error(`獲取電池 ${id} 失敗:`, error);
      return null;
    }
  }

  // 新增電池
  async addBattery(battery: Omit<BatteryDataOut, "id">): Promise<BatteryDataOut | null> {
    try {
      console.log("BatteryApiService add battery", battery);
      const response = await fetch(`${this.apiUrl}/batteries`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(battery)
      });
      console.log("response", response);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result.data;
    } catch (error) {
      console.error("新增電池失敗:", error);
      return null;
    }
  }

  // 修改電池
  async updateBattery(id: string, battery: Partial<Battery>): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(battery)
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`修改電池 ${id} 失敗:`, error);
      return false;
    }
  }

  // 刪除電池
  async deleteBattery(id: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
        method: 'DELETE'
      });
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return true;
    } catch (error) {
      console.error(`刪除電池 ${id} 失敗:`, error);
      return false;
    }
  }
}