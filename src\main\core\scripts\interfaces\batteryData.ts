enum DiagnosisTips
{
    Unknown         = -1,
    SerialNumber    = 0,
    PackVoltage,
    CycleCount,
    Health,
    CellVoltage,
    Unbalance,
    Temperature,
    PFFail,
}

enum BatteryType
{
    Unknown = -1,
    Standard = 0,
    XL,
    XXL,
    Mini,
}

// 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
export interface BatteryDataO {
    id: number,
    sn: string,  // 用來對應 C 的 const char*
    pack: number,
    mode: number,
    designVoltage: number,
    designCapacity: number,
    remainingCapacity: number,
    fullyChargedCapacity: number,
    relativeStateOfCharged: number,
    absoluteStateOfCharged: number,
    untilFullyCharged: number,
    untilFullyDischarged: number,
    cycle: number,
    voltage: number,
    current: number,
    chargingVoltage: number,
    chargingCurrent: number,
    averageCurrent: number,
    temperature: number,
    manufactureDate: string,
    gaugeStatus: number,
    error: number,
    /* Extended Data (XXL) */
    cycleIndex: number,
    cycleThreshold: number,
    fullyChargedDate: string,
    fullyChargedCapacityThreshold: number,
    fullyChargedCapacityBackup: number,
    xxlLifetimeMaxPackVoltage: number,
    xxlLifetimeMinPackVoltage: number,
    xxlLifetimeMaxCurrent: number,
    xxlLifetimeMinCurrent: number,
    orangeLED: number,
    fullyChargedVoltage: number,
    firstUseDate: string,
    recordDate: string,
    recordTime: string,
    packMode: number,
    diagParamPfFlag: boolean,
    /* Extended Data */
    cellVoltage_1: number,
    cellVoltage_2: number,
    cellVoltage_3: number,
    cellVoltage_4: number,
    packVoltage: number,
    fetControl: number,
    safetyAlert_1: number,
    safetyAlert_2: number,
    safetyStatus_1: number,
    safetyStatus_2: number,
    pfAlert_1: number,
    pfAlert_2: number,
    pfStatus_1: number,
    pfStatus_2: number,
    operationStatus: number,
    chargingStatus: number,
    temperatureRange: number,
    maxError: number,
    deviceName: string,
    //maxCellVoltage: number,
    //minCellVoltage: number,
    /* Manufacture Block */
    manufactureBlock_1: string,
    manufactureBlock_2: string,
    manufactureBlock_3: string,
    manufactureBlock_4: string,
    /* Lifetime Data */
    lifetimeMaxTemperature: number,
    lifetimeMinTemperature: number,
    lifetimeAvgTemperature: number,
    lifetimeMaxCellVoltage: number,
    lifetimeMinCellVoltage: number,
    lifetimeMaxPackVoltage: number,
    lifetimeMinPackVoltage: number,
    lifetimeMaxChargingCurrent: number,
    lifetimeMaxDischargingCurrent: number,
    lifetimeMaxAvgDischargingCurrent: number,
    lifetimeMaxChargingPower: number,
    lifetimeMaxDischargingPower: number,
    lifetimeMaxAvgDischargingPower: number,
    lifetimeTemperatureSamples: number,
    otEventCount: number,
    otEventDuration: number,
    ovEventCount: number,
    ovEventDuration: number,
}

export interface BatteryData extends BatteryDataO {
    isComplete: boolean, // 是否取得完整資料
    type: BatteryType, // 電池類型
    health: number, // 電池健康狀況百分比
    verificationResult: {
      isValid: boolean;
      batteryClass: string;
      errorIcons: Array<{ type: string; icon: string; message: string; }>;
      errorDescription: string;
    }
}

export interface BatteryDataOut extends BatteryData {
    chargerSN: string, // 充電器序列號
    typeText: string, // 電池類型文字
    maxCellVoltage: number, 
    minCellVoltage: number,
    isDamaged: boolean, // 是否損壞
    health: number, // 電池健康狀況百分比
    pfFlag: boolean, // 是否有 PFFail // Pass/Fail
    errorStatus: string, // 錯誤狀態
    errorMessage: string, // 錯誤信息
    result: boolean, // 是否成功 // Pass/Fail
    location: string, // 位置
    comment: string, // 備註
}

// 位置管理
export interface BatteryAreaInfo {
    [batteryId: string]: string;
}

// 外觀毀損狀態管理
export interface BatteryDamageInfo {
    [batteryId: string]: boolean;
}




export { DiagnosisTips, BatteryType };